{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport interface JWTPayload {\n  userId: number\n  email: string\n  role: string\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,gBAAgB,QAAgB,EAAE,cAAsB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { comparePassword, generateToken } from '@/lib/auth'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password } = await request.json()\n\n    if (!email || !password) {\n      return NextResponse.json(\n        { error: '邮箱和密码不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 查找用户\n    const user = await prisma.user.findUnique({\n      where: { email }\n    })\n\n    if (!user) {\n      return NextResponse.json(\n        { error: '用户不存在或密码错误' },\n        { status: 401 }\n      )\n    }\n\n    // 验证密码\n    const isValidPassword = await comparePassword(password, user.password)\n    if (!isValidPassword) {\n      return NextResponse.json(\n        { error: '用户不存在或密码错误' },\n        { status: 401 }\n      )\n    }\n\n    // 生成JWT token\n    const token = generateToken({\n      userId: user.id,\n      email: user.email,\n      role: user.role\n    })\n\n    // 设置cookie\n    const response = NextResponse.json({\n      message: '登录成功',\n      user: {\n        id: user.id,\n        email: user.email,\n        username: user.username,\n        role: user.role\n      }\n    })\n\n    response.cookies.set('token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'lax',\n      maxAge: 7 * 24 * 60 * 60 // 7天\n    })\n\n    return response\n  } catch (error) {\n    console.error('登录错误:', error)\n    return NextResponse.json(\n      { error: '服务器内部错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,KAAK,QAAQ;QACrE,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;YAC1B,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB;QAEA,WAAW;QACX,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,SAAS,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO;YACnC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK,GAAG,KAAK;QAChC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}