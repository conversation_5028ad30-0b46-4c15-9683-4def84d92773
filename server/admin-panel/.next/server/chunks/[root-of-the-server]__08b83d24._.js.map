{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/app/api/test/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport async function GET() {\n  return NextResponse.json({ \n    message: 'Hello World!',\n    timestamp: new Date().toISOString(),\n    status: 'success'\n  })\n}\n\nexport async function POST() {\n  return NextResponse.json({ \n    message: 'Hello World from POST!',\n    timestamp: new Date().toISOString(),\n    status: 'success'\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,QAAQ;IACV;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,QAAQ;IACV;AACF", "debugId": null}}]}