{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken'\nimport bcrypt from 'bcryptjs'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport interface JWTPayload {\n  userId: number\n  email: string\n  role: string\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,gBAAgB,QAAgB,EAAE,cAAsB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { verifyToken, hashPassword } from '@/lib/auth'\n\n// 获取用户列表\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get('token')?.value\n    if (!token) {\n      return NextResponse.json({ error: '未登录' }, { status: 401 })\n    }\n\n    const payload = verifyToken(token)\n    if (!payload || payload.role !== 'ADMIN' && payload.role !== 'SUPER_ADMIN') {\n      return NextResponse.json({ error: '权限不足' }, { status: 403 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const skip = (page - 1) * limit\n\n    const [users, total] = await Promise.all([\n      prisma.user.findMany({\n        skip,\n        take: limit,\n        select: {\n          id: true,\n          email: true,\n          username: true,\n          role: true,\n          createdAt: true,\n          updatedAt: true\n        },\n        orderBy: { createdAt: 'desc' }\n      }),\n      prisma.user.count()\n    ])\n\n    return NextResponse.json({\n      users,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    })\n  } catch (error) {\n    console.error('获取用户列表错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n\n// 创建用户\nexport async function POST(request: NextRequest) {\n  try {\n    const token = request.cookies.get('token')?.value\n    if (!token) {\n      return NextResponse.json({ error: '未登录' }, { status: 401 })\n    }\n\n    const payload = verifyToken(token)\n    if (!payload || payload.role !== 'ADMIN' && payload.role !== 'SUPER_ADMIN') {\n      return NextResponse.json({ error: '权限不足' }, { status: 403 })\n    }\n\n    const { email, username, password, role } = await request.json()\n\n    if (!email || !username || !password) {\n      return NextResponse.json({ error: '邮箱、用户名和密码不能为空' }, { status: 400 })\n    }\n\n    // 检查邮箱是否已存在\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { email },\n          { username }\n        ]\n      }\n    })\n\n    if (existingUser) {\n      return NextResponse.json({ error: '邮箱或用户名已存在' }, { status: 400 })\n    }\n\n    const hashedPassword = await hashPassword(password)\n\n    const user = await prisma.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        role: role || 'USER'\n      },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        role: true,\n        createdAt: true,\n        updatedAt: true\n      }\n    })\n\n    return NextResponse.json({ user }, { status: 201 })\n  } catch (error) {\n    console.error('创建用户错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU;QAC5C,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,eAAe;YAC1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAO,GAAG;gBAAE,QAAQ;YAAI;QAC5D;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvC,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB;gBACA,MAAM;gBACN,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,WAAW;gBACb;gBACA,SAAS;oBAAE,WAAW;gBAAO;YAC/B;YACA,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;SAClB;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU;QAC5C,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,eAAe;YAC1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAO,GAAG;gBAAE,QAAQ;YAAI;QAC5D;QAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9D,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,YAAY;QACZ,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE;oBAAM;oBACR;wBAAE;oBAAS;iBACZ;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAY,GAAG;gBAAE,QAAQ;YAAI;QACjE;QAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QAE1C,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV,MAAM,QAAQ;YAChB;YACA,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK,GAAG;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}