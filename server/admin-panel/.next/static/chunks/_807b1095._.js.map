{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function DashboardPage() {\n  const { user, logout, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg\">加载中...</div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                JSO Hunter 管理后台\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">\n                欢迎, {user.username} ({user.role})\n              </span>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                退出登录\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {/* 统计卡片 */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center\">\n                      <span className=\"text-white text-sm font-medium\">U</span>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        用户管理\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        管理系统用户\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 px-5 py-3\">\n                <div className=\"text-sm\">\n                  <button\n                    onClick={() => router.push('/dashboard/users')}\n                    className=\"font-medium text-indigo-700 hover:text-indigo-900\"\n                  >\n                    查看详情\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* API测试卡片 */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <span className=\"text-white text-sm font-medium\">A</span>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        API测试\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        测试API接口\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 px-5 py-3\">\n                <div className=\"text-sm\">\n                  <button\n                    onClick={async () => {\n                      try {\n                        const response = await fetch('/api/test')\n                        const data = await response.json()\n                        alert(JSON.stringify(data, null, 2))\n                      } catch (error) {\n                        alert('API测试失败')\n                      }\n                    }}\n                    className=\"font-medium text-green-700 hover:text-green-900\"\n                  >\n                    测试API\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* 系统信息卡片 */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <span className=\"text-white text-sm font-medium\">S</span>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        系统信息\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        版本 1.0.0\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 px-5 py-3\">\n                <div className=\"text-sm\">\n                  <span className=\"font-medium text-blue-700\">\n                    运行正常\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CACjC,KAAK,QAAQ;4CAAC;4CAAG,KAAK,IAAI;4CAAC;;;;;;;kDAElC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAG3D,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;0CAQP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAG3D,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS;oDACP,IAAI;wDACF,MAAM,WAAW,MAAM,MAAM;wDAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wDAChC,MAAM,KAAK,SAAS,CAAC,MAAM,MAAM;oDACnC,EAAE,OAAO,OAAO;wDACd,MAAM;oDACR;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;0CAQP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAG3D,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D;GArKwB;;QACY,kIAAA,CAAA,UAAO;QAC1B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}