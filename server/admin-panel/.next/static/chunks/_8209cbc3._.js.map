{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\n\ninterface User {\n  id: number\n  email: string\n  username: string\n  role: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport default function UsersPage() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [users, setUsers] = useState<User[]>([])\n  const [loadingUsers, setLoadingUsers] = useState(true)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    } else if (user && (user.role === 'ADMIN' || user.role === 'SUPER_ADMIN')) {\n      fetchUsers()\n    }\n  }, [user, loading, router])\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('/api/users')\n      if (response.ok) {\n        const data = await response.json()\n        setUsers(data.users)\n      } else {\n        setError('获取用户列表失败')\n      }\n    } catch (error) {\n      setError('网络错误')\n    } finally {\n      setLoadingUsers(false)\n    }\n  }\n\n  const handleLogout = async () => {\n    const { logout } = useAuth()\n    await logout()\n    router.push('/login')\n  }\n\n  if (loading || loadingUsers) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg\">加载中...</div>\n      </div>\n    )\n  }\n\n  if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN')) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg text-red-600\">权限不足</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.push('/dashboard')}\n                className=\"text-indigo-600 hover:text-indigo-900\"\n              >\n                ← 返回仪表板\n              </button>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                用户管理\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">\n                {user.username} ({user.role})\n              </span>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                退出登录\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {error && (\n            <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                用户列表\n              </h3>\n              \n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        用户名\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        邮箱\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        角色\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        创建时间\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((userItem) => (\n                      <tr key={userItem.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {userItem.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {userItem.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {userItem.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            userItem.role === 'SUPER_ADMIN' \n                              ? 'bg-red-100 text-red-800'\n                              : userItem.role === 'ADMIN'\n                              ? 'bg-yellow-100 text-yellow-800'\n                              : 'bg-green-100 text-green-800'\n                          }`}>\n                            {userItem.role}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {new Date(userItem.createdAt).toLocaleDateString('zh-CN')}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {users.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  暂无用户数据\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS;;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,QAAQ,CAAC,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,aAAa,GAAG;gBACzE;YACF;QACF;8BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;;QACnB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;QACzB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;QAJM;;YACe,kIAAA,CAAA,UAAO;;;IAK5B,IAAI,WAAW,cAAc;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,eAAgB;QACnE,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAuB;;;;;;;;;;;IAG5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,KAAK,QAAQ;4CAAC;4CAAG,KAAK,IAAI;4CAAC;;;;;;;kDAE9B,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAIjE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,6LAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,6LAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,6LAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,6LAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,6LAAC;oDAAM,WAAU;8DACd,MAAM,GAAG,CAAC,CAAC,yBACV,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,SAAS,EAAE;;;;;;8EAEd,6LAAC;oEAAG,WAAU;8EACX,SAAS,QAAQ;;;;;;8EAEpB,6LAAC;oEAAG,WAAU;8EACX,SAAS,KAAK;;;;;;8EAEjB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAK,WAAW,AAAC,4DAMjB,OALC,SAAS,IAAI,KAAK,gBACd,4BACA,SAAS,IAAI,KAAK,UAClB,kCACA;kFAEH,SAAS,IAAI;;;;;;;;;;;8EAGlB,6LAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;2DAtB5C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;oCA8B3B,MAAM,MAAM,KAAK,mBAChB,6LAAC;wCAAI,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhE;GApKwB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/New%20World/jso-hunter/jso-hunter/server/admin-panel/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}